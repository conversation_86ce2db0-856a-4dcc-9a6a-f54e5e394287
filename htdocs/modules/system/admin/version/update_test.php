<?php
/**
 * ImpressCMS Update Test Script
 *
 * Simple test script to verify the update functionality
 * This file should be removed in production
 *
 * @copyright	The ImpressCMS Project http://www.impresscms.org/
 * @license		http://www.gnu.org/licenses/old-licenses/gpl-2.0.html GNU General Public License (GPL)
 */

// Security check
if (!is_object(icms::$user) || !is_object(icms::$module) || !icms::$user->isAdmin()) {
	exit("Access Denied");
}

echo "<h2>ImpressCMS Update System Test</h2>";

// Test version checker
echo "<h3>1. Testing Version Checker</h3>";
$versionChecker = icms_core_Versioncheckergithub::getInstance();

if ($versionChecker->check()) {
	echo "✓ Version checker working<br>";
	echo "Current version: " . $versionChecker->getInstalledVersionName() . "<br>";
	echo "Latest version: " . $versionChecker->getLatestVersionName() . "<br>";
	echo "Update available: " . ($versionChecker->hasUpdate() ? "Yes" : "No") . "<br>";
	echo "Download URL: " . $versionChecker->getLatestUrl() . "<br>";
} else {
	echo "✗ Version checker failed<br>";
	echo "Errors: " . $versionChecker->getErrors(true) . "<br>";
}

// Test update class instantiation
echo "<h3>2. Testing Update Class</h3>";
try {
	$updater = new icms_core_Update();
	echo "✓ Update class instantiated successfully<br>";
	
	// Test permissions
	echo "Can update: " . (icms_core_Update::canUpdate() ? "Yes" : "No") . "<br>";
	
	// Test directory creation
	echo "Temp directory: " . (is_dir(ICMS_CACHE_PATH . '/updates') ? "✓ Created" : "✗ Not created") . "<br>";
	echo "Backup directory: " . (is_dir(ICMS_CACHE_PATH . '/backups') ? "✓ Created" : "✗ Not created") . "<br>";
	
} catch (Exception $e) {
	echo "✗ Update class failed: " . $e->getMessage() . "<br>";
}

// Test system requirements
echo "<h3>3. Testing System Requirements</h3>";
echo "ZipArchive available: " . (class_exists('ZipArchive') ? "✓ Yes" : "✗ No") . "<br>";
echo "exec() function available: " . (function_exists('exec') ? "✓ Yes" : "✗ No") . "<br>";
echo "file_get_contents() available: " . (function_exists('file_get_contents') ? "✓ Yes" : "✗ No") . "<br>";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? "✓ Enabled" : "✗ Disabled") . "<br>";

// Test file permissions
echo "<h3>4. Testing File Permissions</h3>";
echo "ICMS_ROOT_PATH writable: " . (is_writable(ICMS_ROOT_PATH) ? "✓ Yes" : "✗ No") . "<br>";
echo "ICMS_CACHE_PATH writable: " . (is_writable(ICMS_CACHE_PATH) ? "✓ Yes" : "✗ No") . "<br>";

echo "<h3>5. Test Complete</h3>";
echo "<p><a href='main.php'>← Back to Version Checker</a></p>";
?>
