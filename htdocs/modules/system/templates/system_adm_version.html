<div class="CPbigTitle" style="background-image: url(<{$icms_url}>/modules/system/admin/version/images/version_big.png)"><{$smarty.const._AM_VERSION_TITLE}></div><br />

<div class="head" style="padding: 2px; margin-bottom: 5px;">
	<strong><{$smarty.const._AM_VERSION_YOUR_VERSION}></strong> <{$installed.version_name}>
	<strong>Latest Version</strong> <{$latest.version}>
</div>

<{if $update_available == "1"}>
    <div class="even">
	    <div class="errorMsg">
	        <strong><{$smarty.const._AM_VERSION_UPDATE_NEEDED}></strong><br />
	        <br />
	        <{$smarty.const._AM_VERSION_MOREINFO}><br />
	        <h2><a href="<{$latest.url}>" rel="external"><{$latest.version_name}></a></h2>
		<{if $not_a_final_comment}>
			<div><strong><{$smarty.const._AM_VERSION_WARNING}></strong>: <{$smarty.const._AM_VERSION_WARNING_NOT_A_FINAL}></div>
		<{/if}>
	    </div>
    </div>

    <{if $can_update}>
    <div class="even">
        <h3><{$smarty.const._AM_VERSION_UPDATE_INSTALL}></h3>
        <{if $update_success}>
            <div class="successMsg">
                <strong><{$smarty.const._AM_VERSION_UPDATE_SUCCESS}></strong><br />
                <{$update_messages}>
            </div>
        <{elseif isset($update_errors)}>
            <div class="errorMsg">
                <strong><{$smarty.const._AM_VERSION_UPDATE_FAILED}></strong><br />
                <{$update_errors}><br />
                <{$update_messages}>
            </div>
        <{else}>
            <form method="post" action="" onsubmit="return confirm('<{$smarty.const._AM_VERSION_UPDATE_CONFIRM}>');">
                <input type="hidden" name="action" value="update" />
                <div style="margin-bottom: 10px;">
                    <label for="hash"><{$smarty.const._AM_VERSION_UPDATE_HASH_OPTIONAL}>:</label><br />
                    <input type="text" name="hash" id="hash" placeholder="<{$smarty.const._AM_VERSION_UPDATE_HASH_PLACEHOLDER}>" style="width: 400px;" />
                </div>
                <input type="submit" value="<{$smarty.const._AM_VERSION_UPDATE_START}>" class="formButton" />
            </form>
        <{/if}>
    </div>
    <{else}>
    <div class="even">
        <div class="errorMsg">
            <{$smarty.const._AM_VERSION_UPDATE_PERMISSION_DENIED}>
        </div>
    </div>
    <{/if}>

    <div class="odd">
	    <h2><{$latest.version_name}> <{$smarty.const._AM_VERSION_CHANGELOG}></h2>
   		<p><{$latest.changelog}></p>
    </div>
<{else}>
	<div class="even">
		<{if $errors}>
			<div class="errorMsg">
				<{$errors}>
			</div>
		<{else}>
        	<div class="successMsg"><strong><{$smarty.const._AM_VERSION_NO_UPDATE}></strong></div>
        <{/if}>
    </div>
<{/if}>
<a href="#" onclick ="jQuery('div#system_info').slideToggle();" name='show_info'  id='show_info' value=''><{$smarty.const._AM_VERSION_SYSTEM_INFO}></a>
<{if $can_update}>
| <a href="backup_manager.php">Backup Manager</a>
| <a href="update_test.php">Test Update System</a>
<{/if}>
<div class="system_info" name="system_info" id="system_info" style="padding: 2px; margin-bottom: 5px; display: none;">
    <table>
  <tbody>
    <tr>
      <td><{$smarty.const._AM_VERSION_PHP_SYSTEM}></td>
      <td><{$sysinfo.php.version}></td>
    </tr>
    <tr>
      <td><{$smarty.const._AM_VERSION_MYSQL_SYSTEM}></td>
      <td><{$sysinfo.mysql.version}></td>
    </tr>
    <tr>
      <td><{$smarty.const._AM_VERSION_API_SYSTEM}></td>
      <td><{$sysinfo.php.api}></td>
    </tr>
    <tr>
      <td><{$smarty.const._AM_VERSION_OP_SYSTEM}></td>
      <td><{$sysinfo.os.version}></td>
    </tr>
    <tr>
      <td>register_globals</td>
      <td><{$sysinfo.php.register_globals}></td>
    </tr>
    <tr>
      <td>allow_url_fopen</td>
      <td><{$sysinfo.php.allow_url_fopen}></td>
    </tr>
    <tr>
      <td>fsockopen</td>
      <td><{$sysinfo.php.fsockopen}></td>
    </tr>
    <tr>
      <td>allow_call_time_pass_reference</td>
      <td><{$sysinfo.php.allow_call_time_pass_reference}></td>
    </tr>
    <tr>
      <td>post_max_size</td>
      <td><{$sysinfo.php.post_max_size}></td>
    </tr>
    <tr>
      <td>max_input_time</td>
      <td><{$sysinfo.php.max_input_time}></td>
    </tr>
    <tr>
      <td>output_buffering</td>
      <td><{$sysinfo.php.output_buffering}></td>
    </tr>
    <tr>
      <td>max_execution_time</td>
      <td><{$sysinfo.php.max_execution_time}></td>
    </tr>
    <tr>
      <td>memory_limit</td>
      <td><{$sysinfo.php.memory_limit}></td>
    </tr>
    <tr>
      <td>file_uploads</td>
      <td><{$sysinfo.php.file_uploads}></td>
    </tr>
    <tr>
      <td>upload_max_filesize</td>
      <td><{$sysinfo.php.upload_max_filesize}></td>
    </tr>
  </tbody>
</table>
</div>
